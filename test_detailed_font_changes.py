#!/usr/bin/env python3
"""
Test detailed font changes detection by <PERSON>
"""

import asyncio
from Document_Validation_Agentic import DocumentValidationAgent

async def test_detailed_font_changes():
    """Test Peter's detailed font change detection"""
    
    print("🔍 TESTING DETAILED FONT CHANGES DETECTION")
    print("=" * 60)
    
    # Initialize agent
    agent = DocumentValidationAgent(offline_mode=True)
    
    # File paths
    sample_pdf = "sample/WC4803231023.pdf"
    generated_pdf = "generated/WC 48 03 23_WCP000100020_01_0007252874_0000340043_Gen1.pdf"
    
    try:
        # Extract font information
        print("🔤 Extracting detailed font information...")
        sample_fonts = await agent.pdf_processor.extract_font_information(sample_pdf)
        generated_fonts = await agent.pdf_processor.extract_font_information(generated_pdf)
        
        # Get detailed character information from first page
        sample_page_1 = sample_fonts.get('page_fonts', {}).get(1, {})
        generated_page_1 = generated_fonts.get('page_fonts', {}).get(1, {})
        
        sample_chars = sample_page_1.get('char_details', [])
        generated_chars = generated_page_1.get('char_details', [])
        
        print(f"Sample page 1: {len(sample_chars)} characters")
        print(f"Generated page 1: {len(generated_chars)} characters")
        
        # Group characters into text blocks
        print("\n📝 SAMPLE PDF TEXT BLOCKS:")
        print("-" * 40)
        sample_blocks = agent._group_chars_into_text_blocks(sample_chars)
        for i, block in enumerate(sample_blocks[:15]):  # Show first 15 blocks
            text = block['text'].strip()
            if len(text) > 2:
                print(f"{i+1:2d}. Font: {block['font']:<20} Text: '{text[:50]}{'...' if len(text) > 50 else ''}'")
        
        print(f"\n... and {len(sample_blocks) - 15} more text blocks" if len(sample_blocks) > 15 else "")
        
        print("\n📝 GENERATED PDF TEXT BLOCKS:")
        print("-" * 40)
        generated_blocks = agent._group_chars_into_text_blocks(generated_chars)
        for i, block in enumerate(generated_blocks[:15]):  # Show first 15 blocks
            text = block['text'].strip()
            if len(text) > 2:
                print(f"{i+1:2d}. Font: {block['font']:<20} Text: '{text[:50]}{'...' if len(text) > 50 else ''}'")
        
        print(f"\n... and {len(generated_blocks) - 15} more text blocks" if len(generated_blocks) > 15 else "")
        
        # Test specific text search
        target_texts = [
            "This endorsement changes the policy",
            "Please read it carefully",
            "WISCONSIN DESIGNATED",
            "CANCELLATION ENDORSEMENT",
            "WC 48 03 23",
            "Named Insured",
            "Policy Number"
        ]
        
        print(f"\n🎯 SEARCHING FOR SPECIFIC TEXT FONT CHANGES:")
        print("-" * 50)
        
        for target_text in target_texts:
            print(f"\nSearching for: '{target_text}'")
            
            # Find in sample
            sample_font = None
            for block in sample_blocks:
                if target_text.upper() in block['text'].upper():
                    sample_font = block['font']
                    print(f"  Sample: Found in font '{sample_font}'")
                    break
            
            if not sample_font:
                print(f"  Sample: NOT FOUND")
            
            # Find in generated
            generated_font = None
            for block in generated_blocks:
                if target_text.upper() in block['text'].upper():
                    generated_font = block['font']
                    print(f"  Generated: Found in font '{generated_font}'")
                    break
            
            if not generated_font:
                print(f"  Generated: NOT FOUND")
            
            # Compare fonts
            if sample_font and generated_font:
                if sample_font != generated_font:
                    print(f"  🚨 FONT CHANGE: {sample_font} → {generated_font}")
                else:
                    print(f"  ✅ SAME FONT: {sample_font}")
            else:
                print(f"  ⚠️ Cannot compare - text not found in one or both documents")
        
        # Test Peter's font change detection
        print(f"\n🤖 PETER'S FONT CHANGE DETECTION:")
        print("-" * 40)
        font_changes = agent._find_text_font_changes(sample_fonts, generated_fonts)
        
        if font_changes:
            for i, change in enumerate(font_changes, 1):
                print(f"{i}. {change}")
        else:
            print("No font changes detected by Peter")
        
        # Check if Peter's method is working correctly
        print(f"\n🔧 DEBUGGING PETER'S TEXT MATCHING:")
        print("-" * 40)
        
        # Test text similarity function
        test_pairs = [
            ("This endorsement changes the policy", "This endorsement changes the policy. Please read it carefully."),
            ("WISCONSIN DESIGNATED", "WISCONSIN DESIGNATED NAMED INSURED"),
            ("WC 48 03 23", "WC 48 03 23"),
            ("Named Insured", "Named Insured:")
        ]
        
        for text1, text2 in test_pairs:
            similar = agent._texts_are_similar(text1, text2)
            print(f"'{text1}' vs '{text2}' → Similar: {similar}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_detailed_font_changes())
