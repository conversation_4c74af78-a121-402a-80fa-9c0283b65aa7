#!/usr/bin/env python3
"""
Test Peter's enhanced layout analysis
"""

import asyncio
from Document_Validation_Agentic import DocumentValidationAgent

async def test_peter_analysis():
    """Test Peter's layout analysis specifically"""
    
    print("🧪 TESTING PETER'S ENHANCED LAYOUT ANALYSIS")
    print("=" * 60)
    
    # Initialize agent
    agent = DocumentValidationAgent(offline_mode=True)  # Use offline mode for faster testing
    
    # File paths
    sample_pdf = "sample/WC4803231023.pdf"
    generated_pdf = "generated/WC 48 03 23_WCP000100020_01_0007252874_0000340043_Gen1.pdf"
    
    try:
        # Extract content
        print("📖 Extracting PDF content...")
        sample_content = await agent.pdf_processor.extract_content_with_layout(sample_pdf)
        generated_content = await agent.pdf_processor.extract_content_with_layout(generated_pdf)
        
        print(f"✅ Sample PDF: {len(sample_content)} characters")
        print(f"✅ Generated PDF: {len(generated_content)} characters")
        
        # Test Peter's layout analysis directly
        print("\n🔍 Running Peter's layout analysis...")
        layout_analysis = await agent._analyze_layout_differences(sample_content, generated_content)
        
        print(f"\n📊 PETER'S ANALYSIS RESULTS:")
        print("=" * 50)
        print(f"Agent: {layout_analysis.get('agent_name', 'Unknown')}")
        print(f"Confidence: {layout_analysis.get('agent_confidence', 0)}/100")
        print(f"Layout Score: {layout_analysis.get('layout_score', 0)}/100")
        print(f"Summary: {layout_analysis.get('layout_analysis_summary', 'No summary')}")
        print(f"Structural Consistency: {layout_analysis.get('structural_consistency', 'Unknown')}")
        
        # Show layout differences
        differences = layout_analysis.get('layout_differences', [])
        print(f"\n🎯 LAYOUT DIFFERENCES DETECTED: {len(differences)}")
        print("-" * 40)
        
        for i, diff in enumerate(differences, 1):
            severity_icon = "🚨" if diff.get('severity') == 'CRITICAL' else "⚠️" if diff.get('severity') == 'HIGH' else "ℹ️"
            print(f"{i}. {severity_icon} {diff.get('difference_type', 'Unknown').upper()} ({diff.get('severity', 'Unknown')})")
            print(f"   Location: {diff.get('location', 'Unknown')}")
            print(f"   Sample: {diff.get('sample_layout', 'N/A')}")
            print(f"   Generated: {diff.get('generated_layout', 'N/A')}")
            print(f"   Impact: {diff.get('impact_description', 'Unknown')}")
            print(f"   Peter's Comment: {diff.get('peter_comment', 'N/A')}")
            print()
        
        # Show recommendations
        recommendations = layout_analysis.get('recommendations', [])
        if recommendations:
            print(f"💡 PETER'S RECOMMENDATIONS:")
            print("-" * 30)
            for i, rec in enumerate(recommendations, 1):
                print(f"{i}. {rec}")
        
        print(f"\n🎯 PETER'S EXPERT RECOMMENDATION:")
        print(f"   {layout_analysis.get('peter_expert_recommendation', 'No recommendation')}")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    asyncio.run(test_peter_analysis())
